<template>
  <div v-loading.fullscreen.lock="loading" class="data-analysis-auto-machine-learning-model-management">
    <customize-card :title="$t('soi.autoMachineLearning.modelManagement')">
      <template #header-actions>
        <el-button size="mini" @click="$router.push({ name: 'data-analysis-auto-machine-learning-data-management' })">
          {{ $t('soi.autoMachineLearning.dataManagement') }}
        </el-button>
      </template>
      <div class="ml-button-group">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="deleteCantClick"
          @click="deleteSelectedModel()"
        >
          {{ $t('soi.common.delete') }}
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-edit"
          size="mini"
          :disabled="updateCantClick"
          @click="openUpdateSelectedModel()"
        >
          {{ $t('soi.common.edit') }}
        </el-button>
        <el-button
          class="ml-button-add-model"
          size="mini"
          icon="el-icon-circle-plus-outline"
          @click="addModel()"
        >
          {{ $t('soi.autoMachineLearning.addModel') }}
        </el-button>
      </div>
      <el-card shadow="never" :body-style="{padding: '5px 20px 20px 20px'}">
        <!-- Model Management Table -->
        <el-table
          ref="modelManagementTable"
          height="607px"
          :data="tableData"
          tooltip-effect="dark"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="#" type="index" width="50" />
          <el-table-column
            :label="$t('soi.autoMachineLearning.modelName')"
            prop="modelname"
            align="center"
          />
          <el-table-column :label="$t('soi.common.description')" prop="fulldesc" align="center" />
          <el-table-column :label="$t('soi.autoMachineLearning.framework')" prop="modelfrom" align="center" />
          <el-table-column :label="$t('soi.autoMachineLearning.accuracy')" prop="accuracy" align="center">
            <template slot-scope="scope">
              <!--              <span v-if="scope.row.modelviewname.indexOf('numerical classification') !== -1 && scope.row.modelfrom.toUpperCase() === 'AUTO-SKLEARN'">{{scope.row.accuracy}}</span>-->
              <span>{{ scope.row.accuracy }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.autoMachineLearning.status')" prop="modelstatus" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.modelstatus === 'success'" style="color: #67C23A">{{ scope.row.modelstatus.toUpperCase() }}</span>
              <span v-if="scope.row.modelstatus === 'failed'" style="color: #F56C6C">{{ scope.row.modelstatus.toUpperCase() }}</span>
              <span v-if="scope.row.modelstatus === 'training'" style="color: #109eae">{{ scope.row.modelstatus.toUpperCase() }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.common.createTime')" prop="createtime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center">
            <template slot-scope="scope">
              <div>
                <el-button
                  class="ml-button"
                  size="mini"
                  :disabled="scope.row.modelstatus === 'failed' || scope.row.modelstatus === 'training'"
                  @click="getModelDetails(scope.row)"
                >
                  {{ $t('soi.common.view') }}
                </el-button>
              </div>
              <div>
                <el-button
                  class="ml-button"
                  size="mini"
                  :disabled="scope.row.modelstatus === 'failed' || scope.row.modelstatus === 'training'"
                  @click="openPredictDialog(scope.row)"
                >
                  {{ $t('soi.autoMachineLearning.predict') }}
                </el-button>
              </div>
              <div>
                <el-button
                  style="margin-bottom: 0"
                  class="ml-button"
                  size="mini"
                  :disabled="scope.row.modelstatus === 'failed' || scope.row.modelstatus === 'training'"
                  @click="openBatchPredictDialog(scope.row)"
                >
                  {{ $t('soi.autoMachineLearning.batchPredict') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <el-pagination
            layout="total, prev, pager, next"
            :page-size="pageSize"
            :current-page="currentPage"
            :total="total"
            @current-change="handleCurrentChange"
          />
        </div>

      </el-card>
      <!-- 更新模型信息对话框 -->
      <el-dialog
        :title="$t('soi.autoMachineLearning.editModel')"
        :visible.sync="updateModelDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="50%"
      >
        <div style="width: 600px;margin: 20px auto 0">
          <el-form ref="updateForm" :model="updateForm" label-width="200px" label-position="left">
            <el-form-item :label="$t('soi.autoMachineLearning.modelName')">
              <el-input v-model="updateForm.modelname" maxlength="255" />
            </el-form-item>
            <el-form-item :label="$t('soi.common.description')">
              <el-input v-model="updateForm.fulldesc" type="textarea" maxlength="255" />
            </el-form-item>
            <el-form-item :label="$t('soi.autoMachineLearning.framework')">
              <el-input v-model="updateForm.modelfrom" maxlength="255" disabled />
            </el-form-item>
            <el-form-item :label="$t('soi.autoMachineLearning.accuracy')">
              <el-input v-model="updateForm.accuracy" maxlength="255" disabled />
            </el-form-item>
            <el-form-item :label="$t('soi.autoMachineLearning.status')">
              <el-input v-model="updateForm.modelstatus" maxlength="255" disabled />
            </el-form-item>
            <el-form-item :label="$t('soi.common.createTime')">
              <el-input v-model="updateForm.createtime" maxlength="255" disabled />
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="updateModelDialog = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button type="primary" @click="updateModel()">{{ $t('soi.common.confirm') }}</el-button>
        </span>
      </el-dialog>
      <!-- 模型详情信息对话框 -->
      <el-dialog
        :title="$t('soi.autoMachineLearning.modelDetails')"
        :visible.sync="modelDetailsDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="70%"
      >
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('soi.autoMachineLearning.modelPerformanceCharts')" name="first">
            <el-row class="image-group">
              <div style="height: 323px">
                <div v-if="images.length > 0">
                  <el-col v-for="(item, index) in images" :key="index" :span="8">
                    <el-card shadow="never" class="image-item" :body-style="{padding: '10px 20px'}">
                      <el-image :src="item" />
                    </el-card>
                  </el-col>
                </div>
                <div v-else class="text-center">
                  <span style="display: inline-block;line-height: 323px">{{ $t('soi.autoMachineLearning.noPerformanceChart') }}</span>
                </div>
              </div>
            </el-row>
          </el-tab-pane>
          <el-tab-pane :label="$t('soi.autoMachineLearning.modelStatisticsTable')" name="second">
            <el-row class="image-group">
              <div style="width: 410px;margin: 20px auto 0;height: 303px">
                <el-form label-position="left" inline class="order-info" label-width="250px">
                  <el-form-item v-for="(item, index) in labels" :key="index" :label="item">
                    <span style="display: inline-block;width: 150px">{{ modelStatisticsTable[item] }}</span>
                  </el-form-item>
                </el-form>
              </div>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="modelDetailsDialog = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button size="small" type="primary" @click="modelDetailsDialog = false">{{ $t('soi.common.confirm') }}</el-button>
        </span>
      </el-dialog>
      <!-- 单个预测对话框 -->
      <el-dialog
        :title="$t('soi.autoMachineLearning.predict')"
        :visible.sync="predictDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="50%"
        @closed="handleClosed"
      >
        <div class="single-predict" style="height: 400px;overflow-y: auto">
          <div style="width: 450px;margin: 0 auto;">
            <el-form ref="paramsForm" :model="paramsForm" label-width="200px" label-position="left" size="small">
              <el-form-item v-for="(item, index) in columns" :key="index" :label="item">
                <el-input v-model="paramsForm[item]" />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div style="width: 467px;margin: 20px auto 0;">
          <span v-show="predictionResult.length > 0">{{ `${$t('soi.autoMachineLearning.thePredictionIs')}：${predictionResult}` }}</span>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="predictDialog = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button
            type="primary"
            size="small"
            @click="singlePredict()"
          >{{ $t('soi.autoMachineLearning.predict') }}</el-button>
        </span>
      </el-dialog>
      <!-- 上传数据字典对话框 -->
      <el-dialog
        title="上传数据字典"
        :visible.sync="uploadDataDictDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="50%"
      >
        <div style="width: 400px; margin: 20px auto 0;">
          <el-form label-width="120px" label-position="left">
            <el-form-item label="数据字典文件">
              <el-upload
                ref="dataDictUpload"
                action="/"
                :auto-upload="false"
                :show-file-list="true"
                :drag="true"
                :limit="1"
                accept=".xls,.xlsx"
                :on-change="handleDataDictChange"
                :on-remove="handleDataDictRemove"
                :file-list="dataDictFileList"
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div slot="tip" class="el-upload__tip">只能上传xlsx/xls文件</div>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="uploadDataDictDialog = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button
            type="primary"
            :disabled="dataDictFileList.length === 0"
            @click="confirmUploadDataDict()"
          >{{ $t('soi.common.confirm') }}</el-button>
        </span>
      </el-dialog>
      <!-- 批量预测 -->
      <el-dialog
        :title="$t('soi.autoMachineLearning.batchPredict')"
        :visible.sync="batchPredictDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="60%"
      >
        <el-alert style="margin-bottom: 10px;" :title="$t('soi.autoMachineLearning.batchPredictTips')" type="success" />
        <div style="border-bottom: 1px solid #cccccc;">
          <div class="upload-form">
            <el-form
              ref="uploadForm"
              label-position="left"
              label-width="200px"
              size="small"
            >
              <el-form-item :label="$t('soi.autoMachineLearning.uploadTestFile')">
                <el-upload
                  class="ml-upload"
                  drag
                  :action="uploadUrl"
                  :data="uploadTestFileRequestData"
                  :file-list="fileList"
                  :on-change="handleChange"
                  :on-success="handleSuccess"
                  :before-upload="handleBeforeUpload"
                  :on-error="handleFail"
                  :on-remove="handleRemove"
                  :accept="ACCEPT_FILE_TYPE"
                  :multiple="false"
                >
                  <div class="el-upload__text">{{ $t('soi.autoMachineLearning.uploadFirstTip') }} <em>{{ $t('soi.autoMachineLearning.uploadLastTip') }}</em>
                    <i class="el-icon-upload upload-icon" /></div>
                </el-upload>
                <span v-show="fileList.length === 0" class="ml-upload-tip">{{ $t('soi.autoMachineLearning.uploadAcceptFileType') }}</span>
              </el-form-item>
            </el-form>
          </div>
          <div style="text-align: right;margin-bottom: 5px">
            <el-button type="primary" :disabled="runBatchPredictCantClick" size="mini" @click="batchPredict()">
              {{ $t('soi.autoMachineLearning.predict') }}
            </el-button>
          </div>
        </div>
        <div class="result-table">
          <el-button
            style="margin-bottom: 5px"
            type="danger"
            :disabled="resultDeleteCantClick"
            size="mini"
            icon="el-icon-delete"
            @click="deletePredictResult()"
          >{{ $t('soi.common.delete') }}
          </el-button>
          <el-card shadow="never">
            <el-table :data="resultTableData" height="312" @selection-change="handlePredictFileSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column
                prop="testfilename"
                :label="$t('soi.autoMachineLearning.testFile')"
                align="center"
              />
              <el-table-column
                prop="resultfilename"
                :label="$t('soi.autoMachineLearning.resultFile')"
                align="center"
              />
              <el-table-column prop="status" :label="$t('soi.autoMachineLearning.status')" align="center" />
              <el-table-column prop="info" :label="$t('soi.autoMachineLearning.info')" align="center" />
              <el-table-column :label="$t('soi.common.operate')" align="center" width="280">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    :disabled="scope.row.status !== 'success'"
                    icon="el-icon-download"
                    @click="downloadPredictResultFile(scope.row.resultfilepath)"
                  >{{ $t('soi.common.download') }}
                  </el-button>
                  <el-button
                    type="primary"
                    size="mini"
                    :disabled="scope.row.status !== 'success'"
                    icon="el-icon-pie-chart"
                    @click="importDataAnalysis(scope.row.resultfilepath)"
                  >{{ $t('soi.common.goAnalyze') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="table-footer">
              <el-pagination
                layout="total, prev, pager, next"
                :page-size="predictPageSize"
                :current-page="predictCurrentPage"
                :total="predictTotal"
                @current-change="handlePredictCurrentChange"
              />
            </div>
          </el-card>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="batchPredictDialog = false">{{ $t('soi.common.confirm') }}</el-button>
        </span>
      </el-dialog>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { MACHINE_LEARNING_API_URL } from '@/contains'
import {
  batchPredict,
  deleteModel,
  deleteTestFile,
  getApplications,
  getBatchStatus,
  getPredictResult,
  showModel,
  singlePredict,
  updateModel
} from '@/api/practice/auto-machine-learning'
import { mapGetters } from 'vuex'
import { uploadDataDescription, uploadDataTable } from '@/api/practice/data-import'

export default {
  name: 'AutoMachineLearningModelManagement',
  components: { CustomizeCard },
  data: () => ({
    loading: false,
    deleteCantClick: true,
    updateCantClick: true,
    pageSize: 10,
    currentPage: 1,
    total: 0,
    tableData: [],
    multipleSelection: [],
    updateModelDialog: false,
    updateForm: {},
    activeName: 'first',
    images: [],
    labels: [],
    modelDetailsDialog: false,
    modelStatisticsTable: {},
    predictDialog: false,
    paramsForm: {},
    columns: [],
    predictRequestData: {
      ip: '',
      params: [],
      modelid: '',
      modelpath: '',
      modelfrom: '',
      responsedict: {},
      vectormodeltype: '',
      vectormodelpath: '',
      usemorefeature: '',
      columnsdict: {}
    },
    predictionResult: '',
    keys: [],
    batchPredictDialog: false,
    uploadUrl: '',
    fileList: [],
    ACCEPT_FILE_TYPE: '.csv,.txt',
    uploadTestFileRequestData: {
      userid: '',
      modelid: '',
      columns: ''
    },
    batchPredictRequestData: {
      userid: '',
      fileid: '',
      modelid: '',
      modelpath: '',
      modelfrom: '',
      columns: [],
      columnsdict: {},
      responsedict: {},
      vectormodeltype: '',
      vectormodelpath: '',
      usemorefeature: '',
      columns_index: {}
    },
    resultDeleteCantClick: true,
    predictPageSize: 5,
    predictCurrentPage: 1,
    predictTotal: 0,
    resultTableData: [],
    runBatchPredictCantClick: true,
    multiplePredictFileSelection: [],
    description: [
      'AUC',
      'logloss',
      'mean_per_class_error',
      'RMSE',
      'MSE',
      'deviance',
      'MAE',
      'RMLSE',
      'accuracy',
      'recall accuracy',
      'f1 score',
      'precision score',
      'roc auc score',
      'mean squared error',
      'mean absolute error',
      'median absolute error',
      'r2 score'
    ],
    // 上传数据字典相关数据
    uploadDataDictDialog: false,
    dataDictFileList: [],
    currentPredictResultFilepath: ''
  }),
  computed: {
    ...mapGetters(['userDetails'])
  },
  watch: {
    'multipleSelection'(newValue) {
      // 删除按钮是否能点击
      this.deleteCantClick = newValue.length <= 0
      this.updateCantClick = newValue.length !== 1
    },
    'multiplePredictFileSelection'(newValue) {
      // 删除按钮是否能点击
      this.resultDeleteCantClick = newValue.length <= 0
    }
  },
  created() {
    this.uploadTestFileRequestData.userid = this.userDetails.id
    this.batchPredictRequestData.userid = this.userDetails.id
    this.uploadUrl = `${MACHINE_LEARNING_API_URL}/uploadpredict`
    this.getModels()
  },
  methods: {
    addModel() {
      this.$router.push({ name: 'data-analysis-auto-machine-learning-add-model' })
    },
    // 下载预测结果文件
    downloadPredictResultFile(filepath) {
      window.location.href = `${MACHINE_LEARNING_API_URL}/downloadfile?filepath=${filepath}`
    },
    importDataAnalysis(filepath) {
      // 保存当前预测结果文件路径
      this.currentPredictResultFilepath = filepath
      // 打开上传数据字典对话框
      this.uploadDataDictDialog = true
    },
    // 删除预测结果
    deletePredictResult() {
      const _this = this
      _this.$confirm(_this.$t('soi.autoMachineLearning.deleteTestFileTip'), _this.$t('soi.common.tip'), {
        confirmButtonText: _this.$t('soi.common.confirm'),
        showCancelButton: false,
        type: _this.$t('soi.common.warning')
      }).then(() => {
        _this.loading = true
        const testfileids = { testfileids: [] }
        for (const item of _this.multiplePredictFileSelection) {
          testfileids.testfileids.push(item.testfileid)
        }
        const formData = new FormData()
        formData.append('data', JSON.stringify(testfileids))
        deleteTestFile(formData)
          .then(() => {
            _this.$message.success(_this.$t('soi.autoMachineLearning.deleteSuccessTip'))
            _this.getResultFiles()
          })
          .finally(() => {
            _this.loading = false
          })
      })
    },
    // 获取结果集文件
    getResultFiles() {
      const _this = this
      getPredictResult({ userid: _this.userDetails.id, modelid: _this.batchPredictRequestData.modelid })
        .then(response => {
          _this.predictItems = response.filesinfo
          _this.predictTotal = _this.predictItems.length
          if (_this.predictTotal % _this.predictPageSize === 0) {
            if (_this.predictCurrentPage > _this.predictTotal / _this.predictPageSize) {
              _this.predictCurrentPage = _this.predictCurrentPage - 1
            }
          }
          if (_this.predictCurrentPage === 0) {
            _this.predictCurrentPage = 1
          }
          _this.resultTableData = _this.predictItems.slice((_this.predictCurrentPage - 1) * _this.predictPageSize, _this.predictCurrentPage * _this.predictPageSize)
        })
        .finally(() => {
          _this.loading = false
        })
    },
    // 文件上传成功后获取列信息
    handleSuccess(response) {
      this.batchPredictRequestData.fileid = response.fileid
      this.batchPredictRequestData.columns_index = response.columns_index
      const _this = this
      if (response.result === 'success') {
        this.$message.success(_this.$t('soi.autoMachineLearning.uploadSuccessTip'))
        this.batchPredict()
      } else {
        _this.loading = false
        _this.$message.error(`${_this.$t('soi.autoMachineLearning.uploadFailTip')} [${response.info}]`)
      }
    },
    // 批量预测
    batchPredict() {
      const _this = this
      _this.loading = true
      const formData = new FormData()
      formData.append('data', JSON.stringify(_this.batchPredictRequestData))

      batchPredict(formData)
        .then(() => {
          // 获取结果集文件
          _this.getBatchPredictStatus()
        })
        .finally(() => {
          _this.loading = false
        })
    },
    // 获取批量预测状态
    getBatchPredictStatus() {
      const _this = this
      _this.loading = false
      const timer = setInterval(() => {
        getBatchStatus({ modelid: _this.batchPredictRequestData.modelid })
          .then(response => {
            if (response.info.length > 0) {
              _this.$message.error(response.info)
              clearInterval(timer)
              this.runBatchPredictCantClick = false
              _this.loading = false
            } else {
              if (response.flag) {
                this.runBatchPredictCantClick = false
                _this.$message.success(_this.$t('soi.autoMachineLearning.predictSuccess'))
                this.getResultFiles()
                clearInterval(timer)
                _this.loading = false
              }
            }
          })
          .catch(() => {
            _this.loading = false
            clearInterval(timer)
            this.runBatchPredictCantClick = false
          })
      }, 1000)
    },
    // 打开批量预测对话框
    openBatchPredictDialog(row) {
      // 上传test文件参数
      this.uploadTestFileRequestData.modelid = row.modelid
      this.uploadTestFileRequestData.columns = JSON.stringify(row.columns)
      // 批量预测参数
      this.batchPredictRequestData.modelid = row.modelid
      this.batchPredictRequestData.modelpath = row.modelpath
      this.batchPredictRequestData.modelpath = row.modelpath
      this.batchPredictRequestData.modelfrom = row.modelfrom
      this.batchPredictRequestData.columns = row.columns
      this.batchPredictRequestData.columnsdict = row.columnsdict
      this.batchPredictRequestData.responsedict = row.responsedict
      this.batchPredictRequestData.vectormodeltype = row.vectormodeltype
      this.batchPredictRequestData.vectormodelpath = row.vectormodelpath
      this.batchPredictRequestData.usemorefeature = row.usemorefeature
      this.getResultFiles()
      this.batchPredictDialog = true
    },
    // 清空预测结果
    handleClosed() {
      this.keys = []
      this.predictionResult = ''
    },
    // 单项预测
    singlePredict() {
      const _this = this
      _this.predictRequestData.params[0] = _this.paramsForm
      _this.loading = true
      const formData = new FormData()
      formData.append('data', JSON.stringify(_this.predictRequestData))

      singlePredict(formData)
        .then(response => {
          _this.predictionResult = response.predict
        })
        .finally(() => {
          _this.loading = false
        })
    },
    // 打开单个预测对话框
    openPredictDialog(row) {
      this.predictRequestData.ip = ''
      this.predictRequestData.modelid = row.modelid
      this.predictRequestData.modelpath = row.modelpath
      this.predictRequestData.modelfrom = row.modelfrom
      this.predictRequestData.responsedict = row.responsedict
      this.predictRequestData.vectormodeltype = row.vectormodeltype
      this.predictRequestData.vectormodelpath = row.vectormodelpath
      this.predictRequestData.usemorefeature = row.usemorefeature
      this.predictRequestData.columnsdict = row.columnsdict
      this.columns = row.columns
      const paramsObj = {}
      for (const item of this.columns) {
        paramsObj[item] = 0
      }
      this.paramsForm = paramsObj
      this.predictDialog = true
    },
    // 查看模型详情信息
    getModelDetails(row) {
      this.activeName = 'first'
      this.labels = []
      this.images = []
      this.getImages(row)
    },
    getImages(row) {
      const _this = this
      _this.loading = true

      showModel({ modelid: row.modelid })
        .then(response => {
          const result = response.images
          if (result.length > 0) {
            for (const item of result) {
              this.images.push(`${MACHINE_LEARNING_API_URL}/showdatapic?modelid=${row.modelid}&imagename=${item}`)
            }
          }
          const modelStatisticsTable = row.modelperformance.split(',')
          for (let innerItem of modelStatisticsTable) {
            innerItem = innerItem.split(':')
            this.labels.push(innerItem[0])
            this.modelStatisticsTable[innerItem[0]] = innerItem[1]
          }
          this.modelDetailsDialog = true
        })
        .finally(() => {
          _this.loading = false
        })
    },
    // 更新模型
    updateModel() {
      const _this = this
      _this.$confirm(_this.$t('soi.autoMachineLearning.updateModelTip'), _this.$t('soi.common.tip'), {
        confirmButtonText: _this.$t('soi.common.confirm'),
        showCancelButton: false,
        type: _this.$t('soi.common.warning')
      }).then(() => {
        _this.loading = true
        const requestData = {
          modelid: _this.updateForm.modelid,
          modelname: _this.updateForm.modelname,
          description: _this.updateForm.fulldesc
        }
        const formData = new FormData()
        formData.append('data', JSON.stringify(requestData))

        updateModel(formData)
          .then(() => {
            _this.$message.success(_this.$t('soi.autoMachineLearning.updateModelSuccess'))
            _this.updateModelDialog = false
            _this.getModels()
          })
          .finally(() => {
            _this.loading = false
          })
      })
    },
    // 打开模型对话框
    openUpdateSelectedModel() {
      this.updateForm = JSON.stringify(this.multipleSelection[0])
      this.updateForm = JSON.parse(this.updateForm)
      this.updateModelDialog = true
    },
    // 获取所有的模型列表
    getModels() {
      const _this = this
      _this.loading = true

      getApplications({ userid: _this.userDetails.id })
        .then(response => {
          _this.loading = false
          response.applications.sort((a, b) => {
            return new Date(b.createtime) - new Date(a.createtime)
          })
          _this.items = response.applications
          _this.total = _this.items.length
          if (_this.total % _this.pageSize === 0) {
            if (_this.currentPage > _this.total / _this.pageSize) {
              _this.currentPage = _this.currentPage - 1
            }
          }
          if (_this.currentPage === 0) {
            _this.currentPage = 1
          }
          _this.tableData = _this.items.slice((_this.currentPage - 1) * _this.pageSize, _this.currentPage * _this.pageSize)
        })
        .finally(() => {
          _this.loading = false
        })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.tableData = this.items.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
    },
    handlePredictCurrentChange(currentPage) {
      this.predictCurrentPage = currentPage
      this.resultTableData = this.predictItems.slice((this.predictCurrentPage - 1) * this.predictPageSize, this.predictCurrentPage * this.predictPageSize)
    },
    // 获取表格中选中的行
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },
    handlePredictFileSelectionChange(selection) {
      this.multiplePredictFileSelection = selection
    },
    // 删除选中的模型
    deleteSelectedModel() {
      const _this = this
      _this.$confirm(_this.$t('soi.autoMachineLearning.deleteTip'), _this.$t('soi.common.tip'), {
        confirmButtonText: _this.$t('soi.common.confirm'),
        showCancelButton: false,
        type: _this.$t('soi.common.warning')
      }).then(() => {
        _this.loading = true
        const modelids = { modelids: [], userid: this.userDetails.id }
        for (const item of _this.multipleSelection) {
          modelids.modelids.push(item.modelid)
        }
        const formData = new FormData()
        formData.append('data', JSON.stringify(modelids))

        deleteModel(formData)
          .then(() => {
            _this.$message.success(_this.$t('soi.autoMachineLearning.deleteSuccessTip'))
            _this.getModels()
          })
          .finally(() => {
            _this.loading = false
          })
      })
    },
    // 只显示最新上传成功文件信息
    handleChange(file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      }
    },
    // 上传前显示loading
    handleBeforeUpload() {
      this.loading = true
    },
    handleFail() {
      this.loading = false
    },
    handleRemove() {
      this.fileList = []
    },
    // 处理数据字典文件选择
    handleDataDictChange(file, fileList) {
      if (fileList.length > 0) {
        this.dataDictFileList = [fileList[fileList.length - 1]]
      }
    },
    // 删除数据字典文件
    handleDataDictRemove() {
      this.dataDictFileList = []
    },
    // 确认上传数据字典
    async confirmUploadDataDict() {
      const vue = this
      vue.loading = true

      try {
        // 上传数据字典
        const dataDictFile = this.dataDictFileList[0].raw
        const dataDescFileFormData = new FormData()
        dataDescFileFormData.append('file', dataDictFile)
        dataDescFileFormData.append('userId', this.userDetails.id)
        dataDescFileFormData.append('type', 'aml')

        await uploadDataDescription(dataDescFileFormData)

        // 上传预测结果文件
        const resultData = await fetch(`${MACHINE_LEARNING_API_URL}/downloadfile?filepath=${this.currentPredictResultFilepath}`)
        const resultDataBlob = await resultData.blob()
        const resultDataFilename = this.currentPredictResultFilepath.split('/').filter(part => part !== '').pop()

        const resultDataFile = new File([resultDataBlob], resultDataFilename, {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const resultFileFormData = new FormData()
        resultFileFormData.append('file', resultDataFile, resultDataFilename)
        resultFileFormData.append('userId', this.userDetails.id)
        resultFileFormData.append('type', 'aml')

        await uploadDataTable(resultFileFormData)

        // 关闭对话框
        this.uploadDataDictDialog = false
        // 清空文件列表
        this.dataDictFileList = []
        // 跳转到数据可视化页面
        this.$router.push({ name: 'data-analysis-data-visualization-data-source' })
      } finally {
        vue.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .data-analysis-auto-machine-learning-model-management {
    .ml-title {
      // line-height: 40px;
    }

    .ml-button-group {
      margin: 0 0 5px 0;
      width: 100%;
    }

    .ml-button-add-model {
      float: right;
    }

    .ml-button {
      width: 120px;
      margin-bottom: 3px;
    }

    .image-item {
      border-radius: 0;
    }

    .ml-upload {
      position: relative;
      margin-bottom: 5px;
    }

    .upload-icon {
      position: absolute;
      top: 6px;
      left: 15px;
    }

    ::v-deep .el-list-enter-active,
    ::v-deep .el-list-leave-active {
      transition: none;
    }

    .ml-upload-tip {
      font-size: 12px;
      color: #606266;
      margin-top: 0;
      height: 0;
      position: absolute;
      top: 32px;
    }

    .upload-form {
      width: 500px;
      margin: 0 auto;
    }

    .result-table {
      width: 100%;
      margin: 10px auto 0;
    }
  }
</style>
<style lang="scss">
  .data-analysis-auto-machine-learning-model-management {
    .el-form-item--medium .el-form-item__content, .el-form-item--medium .el-form-item__label {
      line-height: 24px;
    }

    .el-form-item {
      margin-bottom: 20px;
    }

    .single-predict {
      .el-form-item {
        margin-bottom: 10px;
      }
    }

    .el-upload-dragger {
      width: 300px;
      height: 34px;
    }

    .el-upload-dragger .el-icon-upload {
      font-size: 20px;
      color: #22C1E6;
      margin: 0;
      line-height: 1;
    }

    .el-upload-list__item:first-child {
      margin-top: 0;
    }

    .el-upload-list__item:first-child {
      margin-top: 0;
      position: absolute;
      top: 35px;
    }

  }
</style>
