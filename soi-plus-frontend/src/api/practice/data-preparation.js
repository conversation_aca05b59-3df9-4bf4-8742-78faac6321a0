import { get, post } from '@/api/utils/http'
import { DATA_PREPARATION_API_URL } from '@/contains'

export function oneKeyStart(data) {
  return post(DATA_PREPARATION_API_URL + '/one_key_start', data)
}

export function label(data) {
  return post(DATA_PREPARATION_API_URL + '/label', data)
}

export function cleansing(data) {
  return post(DATA_PREPARATION_API_URL + '/cleansing', data)
}

export function featuresSelect(data) {
  return post(DATA_PREPARATION_API_URL + '/features_select', data)
}

export function pcaAnalysis(data) {
  return post(DATA_PREPARATION_API_URL + '/pca_analysis', data)
}

export function balancedDataSet(data) {
  return post(DATA_PREPARATION_API_URL + '/balanced_dataSet', data)
}

export function trainingTestingDataset(data) {
  return post(DATA_PREPARATION_API_URL + '/training_testing_dataset', data)
}

export function getDirty(params) {
  return get(DATA_PREPARATION_API_URL + '/dirty', params)
}

export function getView(params) {
  return get(DATA_PREPARATION_API_URL + '/view', params)
}
